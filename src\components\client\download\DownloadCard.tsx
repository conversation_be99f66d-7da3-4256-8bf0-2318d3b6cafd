import React from 'react';
import { DownloadStatus } from '../../../types/download';
import type { DownloadItem, DownloadProgress } from '../../../types/download';
import DownloadContentCard from './DownloadContentCard';
import { useTranslations } from 'next-intl';

interface DownloadCardProps {
  downloadData: DownloadItem | null;
  downloadProgress: DownloadProgress;
  startDownloadWithData: (data: DownloadItem) => Promise<void>;
  saveToLocal: () => Promise<void>;
  savedFiles: Set<string>;
  updateDownloadDataFilename: (filename: string) => void;
  stopLiveRecording: (requestId: string) => Promise<void>; // 停止直播录制
}

const DownloadCard: React.FC<DownloadCardProps> = ({
  downloadData,
  downloadProgress,
  startDownloadWithData,
  saveToLocal,
  savedFiles,
  updateDownloadDataFilename,
  stopLiveRecording
}) => {
  const t = useTranslations('downloadComponents');

  // 获取当前下载状态 - 无状态时返回 null
  const getCurrentDownloadStatus = (): DownloadStatus | null => {
    if (!downloadData) return null;

    const progress = downloadProgress[downloadData.requestId];
    if (!progress) {
      // 检查是否已保存
      if (savedFiles.has(downloadData.requestId)) {
        return DownloadStatus.SAVED;
      }
      return null;
    }

    return progress.status;
  };

  // 按钮点击处理函数
  const handleRetry = async () => {
    console.log('重试下载');
    if (downloadData) {
      await startDownloadWithData(downloadData);
    }
  };



  const handleSave = async () => {
    console.log('保存到本地');
    if (downloadData) {
      await saveToLocal();
    }
  };

  const handleStopRecording = async () => {
    console.log('停止录制');
    if (downloadData) {
      await stopLiveRecording(downloadData.requestId);
    }
  };

  const handleSaved = async () => {
    console.log('重新保存文件');
    // 已保存状态下点击按钮，触发重新保存
    if (downloadData) {
      await saveToLocal();
    }
  };

  const handleTitleChange = (newTitle: string) => {
    console.log('标题已更改为:', newTitle);
    // 更新下载项的文件名
    updateDownloadDataFilename(newTitle);
  };



  return (
    <div className="flex flex-col justify-center items-center p-6 gap-4 relative w-[1024px] h-80 bg-white shadow-lg rounded-lg">
      {/* 下载内容区域 */}

      {downloadData && (
        <DownloadContentCard
          downloadItem={downloadData}
          progress={downloadProgress[downloadData.requestId] || {
            percentage: 0,
            downloadedSize: 0,
            totalSize: 0,
            speed: 0,
            status: getCurrentDownloadStatus() || DownloadStatus.COMPLETED, // 无状态时显示完成状态，用于显示内容
            statusText: t('waitingDownload')
          }}
          onTitleChange={handleTitleChange}
          onRetry={handleRetry}
          onSave={getCurrentDownloadStatus() === DownloadStatus.SAVED ? handleSaved : handleSave}
          onStopRecording={handleStopRecording}
          savedFiles={savedFiles}
        />
      )}
    </div>
  );
};

export default DownloadCard;
